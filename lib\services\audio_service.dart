import 'package:audioplayers/audioplayers.dart';
import 'package:flutter_vibrate/flutter_vibrate.dart';
import 'storage_service.dart';

class AudioService {
  static final AudioPlayer _audioPlayer = AudioPlayer();
  static bool _soundEnabled = true;
  static bool _vibrationEnabled = true;

  static Future<void> init() async {
    // Load user preferences
    final profile = await StorageService.getUserProfile();
    if (profile != null) {
      _soundEnabled = profile.preferences.soundEnabled;
      _vibrationEnabled = profile.preferences.vibrationEnabled;
    }
  }

  static void updatePreferences({bool? soundEnabled, bool? vibrationEnabled}) {
    if (soundEnabled != null) _soundEnabled = soundEnabled;
    if (vibrationEnabled != null) _vibrationEnabled = vibrationEnabled;
  }

  // Sound Effects
  static Future<void> playCorrectAnswer() async {
    if (!_soundEnabled) return;
    try {
      // For now, we'll use system sounds or create simple tones
      // TODO: Add actual sound files to assets/sounds/
      print('Playing correct answer sound');
    } catch (e) {
      print('Error playing correct answer sound: $e');
    }
  }

  static Future<void> playIncorrectAnswer() async {
    if (!_soundEnabled) return;
    try {
      // For now, we'll use system sounds or create simple tones
      // TODO: Add actual sound files to assets/sounds/
      print('Playing incorrect answer sound');
    } catch (e) {
      print('Error playing incorrect answer sound: $e');
    }
  }

  static Future<void> playButtonClick() async {
    if (!_soundEnabled) return;
    try {
      // For now, we'll use system sounds or create simple tones
      // TODO: Add actual sound files to assets/sounds/
      print('Playing button click sound');
    } catch (e) {
      print('Error playing button click sound: $e');
    }
  }

  static Future<void> playQuizComplete() async {
    if (!_soundEnabled) return;
    try {
      // For now, we'll use system sounds or create simple tones
      // TODO: Add actual sound files to assets/sounds/
      print('Playing quiz complete sound');
    } catch (e) {
      print('Error playing quiz complete sound: $e');
    }
  }

  static Future<void> playTimeWarning() async {
    if (!_soundEnabled) return;
    try {
      // For now, we'll use system sounds or create simple tones
      // TODO: Add actual sound files to assets/sounds/
      print('Playing time warning sound');
    } catch (e) {
      print('Error playing time warning sound: $e');
    }
  }

  // Haptic Feedback
  static Future<void> vibrateCorrect() async {
    if (!_vibrationEnabled) return;
    try {
      if (await Vibrate.canVibrate) {
        Vibrate.feedback(FeedbackType.success);
      }
    } catch (e) {
      print('Error with correct vibration: $e');
    }
  }

  static Future<void> vibrateIncorrect() async {
    if (!_vibrationEnabled) return;
    try {
      if (await Vibrate.canVibrate) {
        Vibrate.feedback(FeedbackType.error);
      }
    } catch (e) {
      print('Error with incorrect vibration: $e');
    }
  }

  static Future<void> vibrateLight() async {
    if (!_vibrationEnabled) return;
    try {
      if (await Vibrate.canVibrate) {
        Vibrate.feedback(FeedbackType.light);
      }
    } catch (e) {
      print('Error with light vibration: $e');
    }
  }

  static Future<void> vibrateHeavy() async {
    if (!_vibrationEnabled) return;
    try {
      if (await Vibrate.canVibrate) {
        Vibrate.feedback(FeedbackType.heavy);
      }
    } catch (e) {
      print('Error with heavy vibration: $e');
    }
  }

  // Combined feedback methods
  static Future<void> correctAnswerFeedback() async {
    await Future.wait([
      playCorrectAnswer(),
      vibrateCorrect(),
    ]);
  }

  static Future<void> incorrectAnswerFeedback() async {
    await Future.wait([
      playIncorrectAnswer(),
      vibrateIncorrect(),
    ]);
  }

  static Future<void> buttonClickFeedback() async {
    await Future.wait([
      playButtonClick(),
      vibrateLight(),
    ]);
  }

  static Future<void> quizCompleteFeedback() async {
    await Future.wait([
      playQuizComplete(),
      vibrateHeavy(),
    ]);
  }

  // Getters
  static bool get soundEnabled => _soundEnabled;
  static bool get vibrationEnabled => _vibrationEnabled;

  // Cleanup
  static Future<void> dispose() async {
    await _audioPlayer.dispose();
  }
}
