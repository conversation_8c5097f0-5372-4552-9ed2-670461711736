import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_profile.dart';
import '../models/quiz_session.dart';

class StorageService {
  static const String _userProfileKey = 'user_profile';
  static const String _quizHistoryKey = 'quiz_history';
  static const String _currentQuizKey = 'current_quiz';
  static const String _themeKey = 'theme_mode';
  static const String _firstLaunchKey = 'first_launch';

  static SharedPreferences? _prefs;

  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  static SharedPreferences get prefs {
    if (_prefs == null) {
      throw Exception('StorageService not initialized. Call init() first.');
    }
    return _prefs!;
  }

  // User Profile Management
  static Future<UserProfile?> getUserProfile() async {
    final String? profileJson = prefs.getString(_userProfileKey);
    if (profileJson == null) return null;
    
    try {
      final Map<String, dynamic> profileMap = json.decode(profileJson);
      return UserProfile.fromJson(profileMap);
    } catch (e) {
      print('Error loading user profile: $e');
      return null;
    }
  }

  static Future<bool> saveUserProfile(UserProfile profile) async {
    try {
      final String profileJson = json.encode(profile.toJson());
      return await prefs.setString(_userProfileKey, profileJson);
    } catch (e) {
      print('Error saving user profile: $e');
      return false;
    }
  }

  static Future<bool> updateUserProfile(UserProfile Function(UserProfile?) updater) async {
    final currentProfile = await getUserProfile();
    final updatedProfile = updater(currentProfile);
    return await saveUserProfile(updatedProfile);
  }

  // Quiz History Management
  static Future<List<QuizSession>> getQuizHistory() async {
    final String? historyJson = prefs.getString(_quizHistoryKey);
    if (historyJson == null) return [];
    
    try {
      final List<dynamic> historyList = json.decode(historyJson);
      return historyList.map((e) => QuizSession.fromJson(e)).toList();
    } catch (e) {
      print('Error loading quiz history: $e');
      return [];
    }
  }

  static Future<bool> saveQuizSession(QuizSession session) async {
    try {
      final List<QuizSession> history = await getQuizHistory();
      history.add(session);
      
      // Keep only last 100 sessions to prevent storage bloat
      if (history.length > 100) {
        history.removeRange(0, history.length - 100);
      }
      
      final String historyJson = json.encode(history.map((e) => e.toJson()).toList());
      return await prefs.setString(_quizHistoryKey, historyJson);
    } catch (e) {
      print('Error saving quiz session: $e');
      return false;
    }
  }

  static Future<List<QuizSession>> getQuizHistoryByCategory(String category) async {
    final List<QuizSession> allHistory = await getQuizHistory();
    return allHistory.where((session) => session.category == category).toList();
  }

  // Current Quiz Progress (for resuming)
  static Future<Map<String, dynamic>?> getCurrentQuizProgress() async {
    final String? progressJson = prefs.getString(_currentQuizKey);
    if (progressJson == null) return null;
    
    try {
      return json.decode(progressJson);
    } catch (e) {
      print('Error loading current quiz progress: $e');
      return null;
    }
  }

  static Future<bool> saveCurrentQuizProgress(Map<String, dynamic> progress) async {
    try {
      final String progressJson = json.encode(progress);
      return await prefs.setString(_currentQuizKey, progressJson);
    } catch (e) {
      print('Error saving current quiz progress: $e');
      return false;
    }
  }

  static Future<bool> clearCurrentQuizProgress() async {
    return await prefs.remove(_currentQuizKey);
  }

  // Theme Management
  static Future<String> getThemeMode() async {
    return prefs.getString(_themeKey) ?? 'system';
  }

  static Future<bool> saveThemeMode(String themeMode) async {
    return await prefs.setString(_themeKey, themeMode);
  }

  // First Launch Check
  static Future<bool> isFirstLaunch() async {
    return prefs.getBool(_firstLaunchKey) ?? true;
  }

  static Future<bool> setFirstLaunchComplete() async {
    return await prefs.setBool(_firstLaunchKey, false);
  }

  // Statistics
  static Future<Map<String, dynamic>> getStatistics() async {
    final List<QuizSession> history = await getQuizHistory();
    final UserProfile? profile = await getUserProfile();
    
    if (history.isEmpty) {
      return {
        'totalQuizzes': 0,
        'totalScore': 0,
        'averageScore': 0.0,
        'bestScore': 0,
        'categoriesPlayed': 0,
        'totalTimePlayed': 0,
      };
    }

    final int totalQuizzes = history.length;
    final int totalScore = history.fold(0, (sum, session) => sum + session.score);
    final double averageScore = totalScore / totalQuizzes;
    final int bestScore = history.map((s) => s.score).reduce((a, b) => a > b ? a : b);
    final Set<String> categoriesPlayed = history.map((s) => s.category).toSet();
    final int totalTimePlayed = history.fold(0, (sum, session) => sum + session.timeTaken.inSeconds);

    return {
      'totalQuizzes': totalQuizzes,
      'totalScore': totalScore,
      'averageScore': averageScore,
      'bestScore': bestScore,
      'categoriesPlayed': categoriesPlayed.length,
      'totalTimePlayed': totalTimePlayed,
      'profile': profile?.toJson(),
    };
  }

  // Clear all data (for reset/logout)
  static Future<bool> clearAllData() async {
    try {
      await prefs.remove(_userProfileKey);
      await prefs.remove(_quizHistoryKey);
      await prefs.remove(_currentQuizKey);
      return true;
    } catch (e) {
      print('Error clearing all data: $e');
      return false;
    }
  }
}
