import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/get_rx.dart';
import '../services/storage_service.dart';
import '../models/user_profile.dart';

class DataController extends GetxController {
  RxString category = ''.obs;
  RxString userName = ''.obs;
  Rx<UserProfile?> userProfile = Rx<UserProfile?>(null);
  RxString difficulty = 'medium'.obs;

  final categoryNameController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    _loadUserProfile();
  }

  Future<void> _loadUserProfile() async {
    try {
      final profile = await StorageService.getUserProfile();
      if (profile != null) {
        userProfile.value = profile;
        userName.value = profile.username;
        difficulty.value = profile.preferences.difficulty;
      }
    } catch (e) {
      print('Error loading user profile: $e');
    }
  }

  void initCategory(String data) {
    category.value = data;
  }

  Future<void> initUsername(String data) async {
    userName.value = data;

    // Create or update user profile
    if (userProfile.value == null) {
      // Create new profile
      final newProfile = UserProfile(
        username: data,
        email: '',
        totalQuizzes: 0,
        totalScore: 0,
        coins: 0,
        rank: 999,
        createdAt: DateTime.now(),
        lastActiveAt: DateTime.now(),
        categoryStats: {},
        preferences: UserPreferences(
          soundEnabled: true,
          vibrationEnabled: true,
          notificationsEnabled: true,
          themeMode: 'system',
          difficulty: 'medium',
        ),
      );
      userProfile.value = newProfile;
      await StorageService.saveUserProfile(newProfile);
    } else {
      // Update existing profile
      final updatedProfile = userProfile.value!.copyWith(
        username: data,
        lastActiveAt: DateTime.now(),
      );
      userProfile.value = updatedProfile;
      await StorageService.saveUserProfile(updatedProfile);
    }
  }

  Future<void> updateUserProfile(UserProfile profile) async {
    userProfile.value = profile;
    await StorageService.saveUserProfile(profile);
  }

  Future<void> setDifficulty(String newDifficulty) async {
    difficulty.value = newDifficulty;
    if (userProfile.value != null) {
      final updatedPreferences = userProfile.value!.preferences.copyWith(
        difficulty: newDifficulty,
      );
      final updatedProfile = userProfile.value!.copyWith(
        preferences: updatedPreferences,
      );
      await updateUserProfile(updatedProfile);
    }
  }

  String get displayName => userName.value.isEmpty ? 'Guest User' : userName.value;
  int get totalCoins => userProfile.value?.coins ?? 0;
  int get userRank => userProfile.value?.rank ?? 999;
}
