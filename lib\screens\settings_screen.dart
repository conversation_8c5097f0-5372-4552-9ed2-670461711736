import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/theme_provider.dart';
import '../controllers/data_controller.dart';
import '../services/audio_service.dart';
import '../models/user_profile.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final ThemeController themeController = Get.find<ThemeController>();
    final DataController dataController = Get.find<DataController>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.blue.shade400, Colors.blue.shade900],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
        ),
      ),
      body: Obx(() {
        final profile = dataController.userProfile.value;
        if (profile == null) {
          return const Center(child: CircularProgressIndicator());
        }

        return ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // User Profile Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Profile',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 16),
                    ListTile(
                      leading: const CircleAvatar(
                        child: Icon(Icons.person),
                      ),
                      title: Text(profile.username),
                      subtitle: Text('${profile.totalQuizzes} quizzes completed'),
                      trailing: IconButton(
                        icon: const Icon(Icons.edit),
                        onPressed: () => _showEditProfileDialog(context, dataController),
                      ),
                    ),
                    const Divider(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildStatCard('Coins', '${profile.coins}', Icons.monetization_on),
                        _buildStatCard('Rank', '#${profile.rank}', Icons.leaderboard),
                        _buildStatCard('Score', '${profile.totalScore}', Icons.star),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Theme Settings
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Appearance',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 16),
                    ListTile(
                      leading: const Icon(Icons.palette),
                      title: const Text('Theme'),
                      subtitle: Text('Current: ${themeController.currentThemeString}'),
                      trailing: DropdownButton<ThemeMode>(
                        value: themeController.themeMode,
                        onChanged: (ThemeMode? newMode) {
                          if (newMode != null) {
                            themeController.setThemeMode(newMode);
                            AudioService.buttonClickFeedback();
                          }
                        },
                        items: const [
                          DropdownMenuItem(
                            value: ThemeMode.system,
                            child: Text('System'),
                          ),
                          DropdownMenuItem(
                            value: ThemeMode.light,
                            child: Text('Light'),
                          ),
                          DropdownMenuItem(
                            value: ThemeMode.dark,
                            child: Text('Dark'),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Audio & Feedback Settings
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Audio & Feedback',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 16),
                    SwitchListTile(
                      secondary: const Icon(Icons.volume_up),
                      title: const Text('Sound Effects'),
                      subtitle: const Text('Play sounds for correct/incorrect answers'),
                      value: profile.preferences.soundEnabled,
                      onChanged: (bool value) {
                        _updatePreference(dataController, 'sound', value);
                        AudioService.updatePreferences(soundEnabled: value);
                      },
                    ),
                    SwitchListTile(
                      secondary: const Icon(Icons.vibration),
                      title: const Text('Vibration'),
                      subtitle: const Text('Haptic feedback for interactions'),
                      value: profile.preferences.vibrationEnabled,
                      onChanged: (bool value) {
                        _updatePreference(dataController, 'vibration', value);
                        AudioService.updatePreferences(vibrationEnabled: value);
                      },
                    ),
                    SwitchListTile(
                      secondary: const Icon(Icons.notifications),
                      title: const Text('Notifications'),
                      subtitle: const Text('Receive quiz reminders and updates'),
                      value: profile.preferences.notificationsEnabled,
                      onChanged: (bool value) {
                        _updatePreference(dataController, 'notifications', value);
                      },
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Quiz Settings
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Quiz Settings',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 16),
                    ListTile(
                      leading: const Icon(Icons.speed),
                      title: const Text('Default Difficulty'),
                      subtitle: Text('Current: ${profile.preferences.difficulty.toUpperCase()}'),
                      trailing: DropdownButton<String>(
                        value: profile.preferences.difficulty,
                        onChanged: (String? newDifficulty) {
                          if (newDifficulty != null) {
                            dataController.setDifficulty(newDifficulty);
                            AudioService.buttonClickFeedback();
                          }
                        },
                        items: const [
                          DropdownMenuItem(value: 'easy', child: Text('Easy')),
                          DropdownMenuItem(value: 'medium', child: Text('Medium')),
                          DropdownMenuItem(value: 'hard', child: Text('Hard')),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Actions
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Actions',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 16),
                    ListTile(
                      leading: const Icon(Icons.history, color: Colors.blue),
                      title: const Text('Quiz History'),
                      subtitle: const Text('View your past quiz results'),
                      onTap: () {
                        // TODO: Navigate to quiz history screen
                        Get.snackbar('Coming Soon', 'Quiz history feature will be available soon!');
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.analytics, color: Colors.green),
                      title: const Text('Statistics'),
                      subtitle: const Text('View detailed performance analytics'),
                      onTap: () {
                        // TODO: Navigate to statistics screen
                        Get.snackbar('Coming Soon', 'Statistics feature will be available soon!');
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.refresh, color: Colors.orange),
                      title: const Text('Reset Progress'),
                      subtitle: const Text('Clear all quiz data and start fresh'),
                      onTap: () => _showResetDialog(context),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 32),
          ],
        );
      }),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, size: 32, color: Colors.blue),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        Text(
          title,
          style: const TextStyle(fontSize: 12, color: Colors.grey),
        ),
      ],
    );
  }

  void _updatePreference(DataController dataController, String type, bool value) {
    final currentProfile = dataController.userProfile.value;
    if (currentProfile == null) return;

    UserPreferences updatedPreferences;
    switch (type) {
      case 'sound':
        updatedPreferences = currentProfile.preferences.copyWith(soundEnabled: value);
        break;
      case 'vibration':
        updatedPreferences = currentProfile.preferences.copyWith(vibrationEnabled: value);
        break;
      case 'notifications':
        updatedPreferences = currentProfile.preferences.copyWith(notificationsEnabled: value);
        break;
      default:
        return;
    }

    final updatedProfile = currentProfile.copyWith(preferences: updatedPreferences);
    dataController.updateUserProfile(updatedProfile);
    AudioService.buttonClickFeedback();
  }

  void _showEditProfileDialog(BuildContext context, DataController dataController) {
    final TextEditingController nameController = TextEditingController();
    nameController.text = dataController.userName.value;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Edit Profile'),
          content: TextField(
            controller: nameController,
            decoration: const InputDecoration(
              labelText: 'Username',
              border: OutlineInputBorder(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                if (nameController.text.trim().isNotEmpty) {
                  dataController.initUsername(nameController.text.trim());
                  Navigator.of(context).pop();
                  Get.snackbar('Success', 'Profile updated successfully!');
                }
              },
              child: const Text('Save'),
            ),
          ],
        );
      },
    );
  }

  void _showResetDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Reset Progress'),
          content: const Text(
            'Are you sure you want to reset all your quiz progress? This action cannot be undone.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                // TODO: Implement reset functionality
                Navigator.of(context).pop();
                Get.snackbar('Coming Soon', 'Reset functionality will be available soon!');
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text('Reset', style: TextStyle(color: Colors.white)),
            ),
          ],
        );
      },
    );
  }
}
