class QuizSession {
  final String id;
  final String category;
  final int score;
  final int totalQuestions;
  final DateTime completedAt;
  final Duration timeTaken;
  final String difficulty;
  final List<QuestionResult> questionResults;

  QuizSession({
    required this.id,
    required this.category,
    required this.score,
    required this.totalQuestions,
    required this.completedAt,
    required this.timeTaken,
    required this.difficulty,
    required this.questionResults,
  });

  double get percentage => (score / totalQuestions) * 100;

  factory QuizSession.fromJson(Map<String, dynamic> json) {
    return QuizSession(
      id: json['id'],
      category: json['category'],
      score: json['score'],
      totalQuestions: json['totalQuestions'],
      completedAt: DateTime.parse(json['completedAt']),
      timeTaken: Duration(seconds: json['timeTaken']),
      difficulty: json['difficulty'] ?? 'medium',
      questionResults: (json['questionResults'] as List)
          .map((e) => QuestionResult.fromJson(e))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'category': category,
      'score': score,
      'totalQuestions': totalQuestions,
      'completedAt': completedAt.toIso8601String(),
      'timeTaken': timeTaken.inSeconds,
      'difficulty': difficulty,
      'questionResults': questionResults.map((e) => e.toJson()).toList(),
    };
  }
}

class QuestionResult {
  final String question;
  final List<String> options;
  final int correctAnswerIndex;
  final int? selectedAnswerIndex;
  final bool isCorrect;
  final Duration timeSpent;

  QuestionResult({
    required this.question,
    required this.options,
    required this.correctAnswerIndex,
    this.selectedAnswerIndex,
    required this.isCorrect,
    required this.timeSpent,
  });

  factory QuestionResult.fromJson(Map<String, dynamic> json) {
    return QuestionResult(
      question: json['question'],
      options: List<String>.from(json['options']),
      correctAnswerIndex: json['correctAnswerIndex'],
      selectedAnswerIndex: json['selectedAnswerIndex'],
      isCorrect: json['isCorrect'],
      timeSpent: Duration(seconds: json['timeSpent']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'question': question,
      'options': options,
      'correctAnswerIndex': correctAnswerIndex,
      'selectedAnswerIndex': selectedAnswerIndex,
      'isCorrect': isCorrect,
      'timeSpent': timeSpent.inSeconds,
    };
  }
}
