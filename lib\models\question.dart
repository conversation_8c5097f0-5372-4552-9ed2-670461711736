class Question {
  final String question;
  final List<String> options;
  int correctAnswerIndex;
  final String? difficulty;
  final String? category;
  final String? hint;

  Question({
    required this.correctAnswerIndex,
    required this.question,
    required this.options,
    this.difficulty,
    this.category,
    this.hint,
  });

  factory Question.fromJson(Map<String, dynamic> json) {
    return Question(
      question: json['question'],
      options: List<String>.from(json['options']),
      correctAnswerIndex: json['correctAnswerIndex'],
      difficulty: json['difficulty'],
      category: json['category'],
      hint: json['hint'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'question': question,
      'options': options,
      'correctAnswerIndex': correctAnswerIndex,
      'difficulty': difficulty,
      'category': category,
      'hint': hint,
    };
  }
}
