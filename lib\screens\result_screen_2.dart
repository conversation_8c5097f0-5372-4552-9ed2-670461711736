import 'package:confetti/confetti.dart';
import 'package:flutter/material.dart';
import 'package:flutter_quiz_app_project/screens/category_screen_2.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:math';
import '/controllers/data_controller.dart';
import '/controllers/result_controller.dart';
import '../services/audio_service.dart';

class ResultScreen extends StatelessWidget {
  ResultScreen({
    super.key,
    required this.score,
    required this.totalQuestions,
  });

  final int score;
  final int totalQuestions;

  @override
  Widget build(BuildContext context) {
    final DataController dataController = Get.find<DataController>();
    final ResultController resultController =
        Get.put(ResultController(score: score, totalQuestions: totalQuestions));

    int perc = (score / totalQuestions * 100).round();
    String msg = '';
    if (perc >= 80) {
      msg = 'Congratulations!';
    } else if (perc < 80 && perc >= 35) {
      msg = 'Well played';
    } else {
      msg = 'Nice try';
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Quiz Results'),
      ),
      body: Stack(
        children: [
          Container(
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage("assets/result_image.png"),
                fit: BoxFit.cover,
              ),
            ),
          ),
          Align(
            alignment: Alignment.topCenter,
            child: GetBuilder<ResultController>(
              builder: (controller) {
                return ConfettiWidget(
                  confettiController: controller.confettiController,
                  blastDirection: -pi / 2,
                  blastDirectionality: BlastDirectionality.explosive,
                  maxBlastForce: 20,
                  minBlastForce: 8,
                  emissionFrequency: 0.2,
                  numberOfParticles: 8,
                  gravity: 0.08,
                  colors: const [
                    Colors.green,
                    Colors.blue,
                    Colors.pink,
                    Colors.orange,
                    Colors.purple,
                  ],
                );
              },
            ),
          ),
          GetBuilder<ResultController>(
            builder: (controller) {
              return AnimatedBuilder(
                animation: controller.animation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: controller.animation.value,
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          Text(
                            '$msg\n${dataController.userName.value}\nYour Score:',
                            style: const TextStyle(
                              fontSize: 34,
                              fontWeight: FontWeight.w500,
                              color: Colors.white,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          Stack(
                            alignment: Alignment.center,
                            children: [
                              SizedBox(
                                height: 250,
                                width: 250,
                                child: CircularProgressIndicator(
                                  strokeWidth: 10,
                                  value: score /
                                      totalQuestions, // Calculate percentage based on total questions
                                  color: Colors.green,
                                  backgroundColor: Colors.white,
                                ),
                              ),
                              Column(
                                children: [
                                  Text(
                                    score.toString(),
                                    style: const TextStyle(
                                      fontSize: 80,
                                      color: Colors.white,
                                    ),
                                  ),
                                  const SizedBox(height: 10),
                                  Text(
                                    '${(score / totalQuestions * 100).round()}%', // Calculate percentage based on total questions
                                    style: const TextStyle(
                                      fontSize: 25,
                                      color: Colors.white,
                                    ),
                                  )
                                ],
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ],
      ),
      floatingActionButton: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            FloatingActionButton(
              onPressed: () async {
                await AudioService.buttonClickFeedback();
                await _shareResult();
              },
              child: Icon(Icons.share),
            ),
            FloatingActionButton(
              onPressed: () async {
                await AudioService.buttonClickFeedback();
                Get.offAll(CategoryScreen());
              },
              child: Icon(Icons.refresh),
            ),
          ],
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Future<void> _shareResult() async {
    final DataController dataController = Get.find<DataController>();
    final percentage = (score / totalQuestions * 100).round();

    String performanceText;
    String emoji;

    if (percentage >= 90) {
      performanceText = "Excellent! 🌟";
      emoji = "🏆";
    } else if (percentage >= 80) {
      performanceText = "Great job! 👏";
      emoji = "🎉";
    } else if (percentage >= 70) {
      performanceText = "Good work! 👍";
      emoji = "😊";
    } else if (percentage >= 60) {
      performanceText = "Not bad! 🤔";
      emoji = "📚";
    } else {
      performanceText = "Keep practicing! 💪";
      emoji = "📖";
    }

    final shareText = '''
$emoji QuizFlick Results $emoji

Category: ${dataController.category.value}
Score: $score/$totalQuestions ($percentage%)
$performanceText

Challenge your friends to beat this score! 🚀

#QuizFlick #Quiz #Learning #Challenge
    '''.trim();

    try {
      await Share.share(shareText);
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to share results. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }
}
