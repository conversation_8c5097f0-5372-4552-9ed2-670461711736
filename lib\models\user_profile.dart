class UserProfile {
  final String username;
  final String email;
  final int totalQuizzes;
  final int totalScore;
  final int coins;
  final int rank;
  final DateTime createdAt;
  final DateTime lastActiveAt;
  final Map<String, CategoryStats> categoryStats;
  final UserPreferences preferences;

  UserProfile({
    required this.username,
    required this.email,
    required this.totalQuizzes,
    required this.totalScore,
    required this.coins,
    required this.rank,
    required this.createdAt,
    required this.lastActiveAt,
    required this.categoryStats,
    required this.preferences,
  });

  double get averageScore => totalQuizzes > 0 ? totalScore / totalQuizzes : 0.0;

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      username: json['username'],
      email: json['email'] ?? '',
      totalQuizzes: json['totalQuizzes'] ?? 0,
      totalScore: json['totalScore'] ?? 0,
      coins: json['coins'] ?? 0,
      rank: json['rank'] ?? 999,
      createdAt: DateTime.parse(json['createdAt']),
      lastActiveAt: DateTime.parse(json['lastActiveAt']),
      categoryStats: (json['categoryStats'] as Map<String, dynamic>? ?? {})
          .map((key, value) => MapEntry(key, CategoryStats.fromJson(value))),
      preferences: UserPreferences.fromJson(json['preferences'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'username': username,
      'email': email,
      'totalQuizzes': totalQuizzes,
      'totalScore': totalScore,
      'coins': coins,
      'rank': rank,
      'createdAt': createdAt.toIso8601String(),
      'lastActiveAt': lastActiveAt.toIso8601String(),
      'categoryStats': categoryStats.map((key, value) => MapEntry(key, value.toJson())),
      'preferences': preferences.toJson(),
    };
  }

  UserProfile copyWith({
    String? username,
    String? email,
    int? totalQuizzes,
    int? totalScore,
    int? coins,
    int? rank,
    DateTime? lastActiveAt,
    Map<String, CategoryStats>? categoryStats,
    UserPreferences? preferences,
  }) {
    return UserProfile(
      username: username ?? this.username,
      email: email ?? this.email,
      totalQuizzes: totalQuizzes ?? this.totalQuizzes,
      totalScore: totalScore ?? this.totalScore,
      coins: coins ?? this.coins,
      rank: rank ?? this.rank,
      createdAt: createdAt,
      lastActiveAt: lastActiveAt ?? this.lastActiveAt,
      categoryStats: categoryStats ?? this.categoryStats,
      preferences: preferences ?? this.preferences,
    );
  }
}

class CategoryStats {
  final int quizzesPlayed;
  final int totalScore;
  final int bestScore;
  final DateTime lastPlayed;

  CategoryStats({
    required this.quizzesPlayed,
    required this.totalScore,
    required this.bestScore,
    required this.lastPlayed,
  });

  double get averageScore => quizzesPlayed > 0 ? totalScore / quizzesPlayed : 0.0;

  factory CategoryStats.fromJson(Map<String, dynamic> json) {
    return CategoryStats(
      quizzesPlayed: json['quizzesPlayed'] ?? 0,
      totalScore: json['totalScore'] ?? 0,
      bestScore: json['bestScore'] ?? 0,
      lastPlayed: DateTime.parse(json['lastPlayed']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'quizzesPlayed': quizzesPlayed,
      'totalScore': totalScore,
      'bestScore': bestScore,
      'lastPlayed': lastPlayed.toIso8601String(),
    };
  }
}

class UserPreferences {
  final bool soundEnabled;
  final bool vibrationEnabled;
  final bool notificationsEnabled;
  final String themeMode; // 'light', 'dark', 'system'
  final String difficulty; // 'easy', 'medium', 'hard'

  UserPreferences({
    required this.soundEnabled,
    required this.vibrationEnabled,
    required this.notificationsEnabled,
    required this.themeMode,
    required this.difficulty,
  });

  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      soundEnabled: json['soundEnabled'] ?? true,
      vibrationEnabled: json['vibrationEnabled'] ?? true,
      notificationsEnabled: json['notificationsEnabled'] ?? true,
      themeMode: json['themeMode'] ?? 'system',
      difficulty: json['difficulty'] ?? 'medium',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'soundEnabled': soundEnabled,
      'vibrationEnabled': vibrationEnabled,
      'notificationsEnabled': notificationsEnabled,
      'themeMode': themeMode,
      'difficulty': difficulty,
    };
  }

  UserPreferences copyWith({
    bool? soundEnabled,
    bool? vibrationEnabled,
    bool? notificationsEnabled,
    String? themeMode,
    String? difficulty,
  }) {
    return UserPreferences(
      soundEnabled: soundEnabled ?? this.soundEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      themeMode: themeMode ?? this.themeMode,
      difficulty: difficulty ?? this.difficulty,
    );
  }
}
