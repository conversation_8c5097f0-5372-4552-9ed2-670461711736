import 'dart:convert';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '/models/question.dart';
import '/models/quiz_session.dart';
import '/models/user_profile.dart';
import '/controllers/data_controller.dart';
import '../services/storage_service.dart';
import '../services/audio_service.dart';

class QuizController extends GetxController {
  var questions = <Question>[].obs;
  var questionIndex = 0.obs;
  var selectedAnswerIndex = Rxn<int>();
  var score = 0.obs;
  var isLoading = false.obs;
  var questionResults = <QuestionResult>[].obs;
  var quizStartTime = DateTime.now().obs;
  var questionStartTime = DateTime.now().obs;
  var usedQuestions = <String>[].obs; // Track used questions to prevent repetition
  var hintsUsed = 0.obs;
  var lifelinesUsed = 0.obs;

  DataController dataController = Get.find<DataController>();

  @override
  void onInit() {
    super.onInit();
    quizStartTime.value = DateTime.now();
    loadQuestions();
  }

  @override
  void onClose() {
    // Save current progress before closing
    _saveCurrentProgress();
    super.onClose();
  }

  Future<void> loadQuestions() async {
    if (dataController.category.value == 'General Knowledge') {
      String data = await DefaultAssetBundle.of(Get.context!)
          .loadString('assets/questions.json');
      final List<dynamic> jsonResult = json.decode(data);
      questions.value = jsonResult.map((e) => Question.fromJson(e)).toList();
    } else if (dataController.category.value == 'Science') {
      String data = await DefaultAssetBundle.of(Get.context!)
          .loadString('assets/science_questions.json');
      final List<dynamic> jsonResult = json.decode(data);
      questions.value = jsonResult.map((e) => Question.fromJson(e)).toList();
    } else if (dataController.category.value == 'History') {
      String data = await DefaultAssetBundle.of(Get.context!)
          .loadString('assets/history_questions.json');
      final List<dynamic> jsonResult = json.decode(data);
      questions.value = jsonResult.map((e) => Question.fromJson(e)).toList();
    } else if (dataController.category.value == 'Geography') {
      String data = await DefaultAssetBundle.of(Get.context!)
          .loadString('assets/geography_questions.json');
      final List<dynamic> jsonResult = json.decode(data);
      questions.value = jsonResult.map((e) => Question.fromJson(e)).toList();
    } else if (dataController.category.value == 'Computer') {
      String data = await DefaultAssetBundle.of(Get.context!)
          .loadString('assets/computer_questions.json');
      final List<dynamic> jsonResult = json.decode(data);
      questions.value = jsonResult.map((e) => Question.fromJson(e)).toList();
    }

    // Shuffle and pick 10 questions
    questions.shuffle(Random());
    questions.value = questions.take(10).toList();

    questions.forEach(_shuffleOptionsAndCorrectAnswer);
  }

  Future<void> pickAnswer(int value) async {
    selectedAnswerIndex.value = value;
    final question = questions[questionIndex.value];
    final isCorrect = selectedAnswerIndex.value == question.correctAnswerIndex;

    // Play audio feedback
    if (isCorrect) {
      score.value++;
      await AudioService.correctAnswerFeedback();
    } else {
      await AudioService.incorrectAnswerFeedback();
    }

    // Record question result
    final timeSpent = DateTime.now().difference(questionStartTime.value);
    final questionResult = QuestionResult(
      question: question.question,
      options: question.options,
      correctAnswerIndex: question.correctAnswerIndex,
      selectedAnswerIndex: value,
      isCorrect: isCorrect,
      timeSpent: timeSpent,
    );
    questionResults.add(questionResult);
  }

  Future<void> goToNextQuestion() async {
    await AudioService.buttonClickFeedback();

    if (questionIndex.value < questions.length - 1) {
      questionIndex.value++;
      selectedAnswerIndex.value = null;
      questionStartTime.value = DateTime.now();
      await _saveCurrentProgress();
    }
  }

  void _shuffleOptionsAndCorrectAnswer(Question question) {
    // final originalOptions = List<String>.from(question.options);
    final originalCorrectAnswer = question.options[question.correctAnswerIndex];
    question.options.shuffle();
    question.correctAnswerIndex =
        question.options.indexOf(originalCorrectAnswer);
  }

  // Save current quiz progress
  Future<void> _saveCurrentProgress() async {
    final progress = {
      'category': dataController.category.value,
      'questionIndex': questionIndex.value,
      'score': score.value,
      'questions': questions.map((q) => q.toJson()).toList(),
      'questionResults': questionResults.map((r) => r.toJson()).toList(),
      'quizStartTime': quizStartTime.value.toIso8601String(),
      'hintsUsed': hintsUsed.value,
      'lifelinesUsed': lifelinesUsed.value,
    };
    await StorageService.saveCurrentQuizProgress(progress);
  }

  // Load saved quiz progress
  Future<bool> loadSavedProgress() async {
    final progress = await StorageService.getCurrentQuizProgress();
    if (progress == null) return false;

    try {
      dataController.category.value = progress['category'] ?? '';
      questionIndex.value = progress['questionIndex'] ?? 0;
      score.value = progress['score'] ?? 0;
      hintsUsed.value = progress['hintsUsed'] ?? 0;
      lifelinesUsed.value = progress['lifelinesUsed'] ?? 0;

      if (progress['questions'] != null) {
        questions.value = (progress['questions'] as List)
            .map((q) => Question.fromJson(q))
            .toList();
      }

      if (progress['questionResults'] != null) {
        questionResults.value = (progress['questionResults'] as List)
            .map((r) => QuestionResult.fromJson(r))
            .toList();
      }

      if (progress['quizStartTime'] != null) {
        quizStartTime.value = DateTime.parse(progress['quizStartTime']);
      }

      questionStartTime.value = DateTime.now();
      return true;
    } catch (e) {
      print('Error loading saved progress: $e');
      return false;
    }
  }

  // Complete quiz and save session
  Future<void> completeQuiz() async {
    await AudioService.quizCompleteFeedback();

    final totalTime = DateTime.now().difference(quizStartTime.value);
    final session = QuizSession(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      category: dataController.category.value,
      score: score.value,
      totalQuestions: questions.length,
      completedAt: DateTime.now(),
      timeTaken: totalTime,
      difficulty: dataController.difficulty.value,
      questionResults: questionResults.toList(),
    );

    await StorageService.saveQuizSession(session);
    await StorageService.clearCurrentQuizProgress();

    // Update user profile
    await _updateUserStats(session);
  }

  // Update user statistics
  Future<void> _updateUserStats(QuizSession session) async {
    await StorageService.updateUserProfile((currentProfile) {
      if (currentProfile == null) {
        // Create a default profile if none exists
        return UserProfile(
          username: dataController.userName.value,
          email: '',
          totalQuizzes: 1,
          totalScore: session.score,
          coins: session.score * 10,
          rank: 999,
          createdAt: DateTime.now(),
          lastActiveAt: DateTime.now(),
          categoryStats: {
            session.category: CategoryStats(
              quizzesPlayed: 1,
              totalScore: session.score,
              bestScore: session.score,
              lastPlayed: session.completedAt,
            ),
          },
          preferences: UserPreferences(
            soundEnabled: true,
            vibrationEnabled: true,
            notificationsEnabled: true,
            themeMode: 'system',
            difficulty: 'medium',
          ),
        );
      }

      final updatedStats = Map<String, CategoryStats>.from(currentProfile.categoryStats);
      final categoryKey = session.category;

      if (updatedStats.containsKey(categoryKey)) {
        final currentStats = updatedStats[categoryKey]!;
        updatedStats[categoryKey] = CategoryStats(
          quizzesPlayed: currentStats.quizzesPlayed + 1,
          totalScore: currentStats.totalScore + session.score,
          bestScore: session.score > currentStats.bestScore ? session.score : currentStats.bestScore,
          lastPlayed: session.completedAt,
        );
      } else {
        updatedStats[categoryKey] = CategoryStats(
          quizzesPlayed: 1,
          totalScore: session.score,
          bestScore: session.score,
          lastPlayed: session.completedAt,
        );
      }

      return currentProfile.copyWith(
        totalQuizzes: currentProfile.totalQuizzes + 1,
        totalScore: currentProfile.totalScore + session.score,
        lastActiveAt: DateTime.now(),
        categoryStats: updatedStats,
        coins: currentProfile.coins + (session.score * 10), // 10 coins per correct answer
      );
    });
  }

  // Lifeline: 50-50 (remove 2 wrong answers)
  void useFiftyFifty() {
    if (lifelinesUsed.value >= 1) return; // Only one lifeline per quiz

    final question = questions[questionIndex.value];
    final correctIndex = question.correctAnswerIndex;
    final wrongIndices = <int>[];

    for (int i = 0; i < question.options.length; i++) {
      if (i != correctIndex) {
        wrongIndices.add(i);
      }
    }

    wrongIndices.shuffle();
    // Remove 2 wrong answers (keep correct + 1 wrong)
    // This will be handled in the UI by checking lifelinesUsed

    lifelinesUsed.value++;
    AudioService.buttonClickFeedback();
  }

  // Hint system
  String? getHint() {
    if (hintsUsed.value >= 3) return null; // Max 3 hints per quiz

    final question = questions[questionIndex.value];
    if (question.hint != null && question.hint!.isNotEmpty) {
      hintsUsed.value++;
      AudioService.buttonClickFeedback();
      return question.hint;
    }

    // Generate generic hint if no specific hint available
    hintsUsed.value++;
    AudioService.buttonClickFeedback();
    return "Think carefully about the question and eliminate obviously wrong answers.";
  }

  // Reset quiz
  void resetQuiz() {
    questionIndex.value = 0;
    selectedAnswerIndex.value = null;
    score.value = 0;
    questionResults.clear();
    hintsUsed.value = 0;
    lifelinesUsed.value = 0;
    quizStartTime.value = DateTime.now();
    questionStartTime.value = DateTime.now();
    StorageService.clearCurrentQuizProgress();
  }

  // Getters
  bool get isQuizComplete => questionIndex.value >= questions.length - 1;
  double get progressPercentage => questions.isEmpty ? 0.0 : (questionIndex.value + 1) / questions.length;
  Duration get elapsedTime => DateTime.now().difference(quizStartTime.value);
  bool get canUseFiftyFifty => lifelinesUsed.value < 1;
  bool get canUseHint => hintsUsed.value < 3;
}
