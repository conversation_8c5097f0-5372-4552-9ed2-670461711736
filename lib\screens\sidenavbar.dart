import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '/controllers/theme_provider.dart';
import '/controllers/data_controller.dart';

class SideNav extends StatelessWidget {
  const SideNav({super.key});

  @override
  Widget build(BuildContext context) {
    final ThemeController themeController = Get.find();
    final DataController dataController = Get.find<DataController>();

    return Drawer(
      child: Material(
        color: Color.fromRGBO(54, 102, 245, 0.858),
        child: ListView(
          children: [
            Container(
              padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: Colors.white,
                  ),
                  Sized<PERSON><PERSON>(width: 20),
                  Obx(() => Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        dataController.displayName,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      <PERSON>zed<PERSON><PERSON>(height: 10),
                      Text(
                        "${dataController.totalCoins} coins",
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 15,
                        ),
                      ),
                    ],
                  ))
                ],
              ),
            ),
            Container(
              padding: EdgeInsets.only(left: 25),
              child: Obx(() => Text(
                "Leaderboard - ${dataController.userRank}${_getOrdinalSuffix(dataController.userRank)} Rank",
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 19,
                  fontWeight: FontWeight.bold,
                ),
              )),
            ),
            SizedBox(height: 48),
            listItem(
              label: "DAILY QUIZ",
              icon: Icons.quiz,
            ),
            listItem(
              label: "Leaderboard",
              icon: Icons.leaderboard,
            ),
            listItem(
              label: "How To Use",
              icon: Icons.question_answer,
            ),
            listItem(
              label: "About Us",
              icon: Icons.face,
            ),
            listItem(
              label: "Contact Us",
              icon: Icons.contact_page,
            ),
            listItem(
              label: "Terms & Conditions",
              icon: Icons.rule,
            ),
            Divider(color: Colors.white),
            ListTile(
              leading: Icon(Icons.palette, color: Colors.white),
              title: Text(
                'Toggle Theme',
                style: TextStyle(color: Colors.white),
              ),
              onTap: () {
                themeController.toggleTheme();
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget listItem({
    required String label,
    required IconData icon,
  }) {
    final color = Colors.white;
    final hovercolor = Colors.white60;

    return ListTile(
      leading: Icon(icon, color: color),
      hoverColor: hovercolor,
      title: Text(label, style: TextStyle(color: color)),
      onTap: () {
        // TODO: Implement navigation for each menu item
        Get.snackbar(
          'Coming Soon',
          '$label feature will be available soon!',
          snackPosition: SnackPosition.BOTTOM,
        );
      },
    );
  }

  String _getOrdinalSuffix(int number) {
    if (number >= 11 && number <= 13) {
      return 'th';
    }
    switch (number % 10) {
      case 1:
        return 'st';
      case 2:
        return 'nd';
      case 3:
        return 'rd';
      default:
        return 'th';
    }
  }
}
